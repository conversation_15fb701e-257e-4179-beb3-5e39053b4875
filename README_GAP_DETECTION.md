# TPDOG监控程序资金断层检测功能

## 功能说明

本次修改为TPDOG版块监控程序添加了资金断层检测功能，可以像`dynamic_gap_detector.py`一样分析概念和行业板块的资金断层情况。

## 修改内容

### 1. start_tpdog_monitor.py
- 添加了资金断层检测模块的导入
- 增加了模块可用性检查

### 2. tpdog_sector_monitor.py
- 导入了断层检测相关函数
- 新增了`analyze_sector_gap()`函数，用于分析板块资金断层
- 在获取行业和概念板块数据后自动进行断层分析
- 修改了金额格式化函数名，避免命名冲突

## 功能特点

### 断层检测算法
- **智能断层识别**: 基于市场状态的动态多维度分析
- **格局类型识别**: 单龙头断层、双强争霸、三足鼎立等
- **市场状态感知**: 超大资金、大资金、中等资金、小资金市场分类
- **综合评分系统**: 多维度评分，超过1.5分阈值触发警报

### 输出示例
```
【★★★★★ 概念板块发现资金断层! ★★★★★】
  格局类型: 单龙头断层
  断层龙头: 【人工智能】
  断层分析: 在第1名后发现显著断层，绝对差距65.00亿元，相对差距1.76倍
  市场状态: 超大资金市场 (总计374.0亿)，集中度62.8%
  综合得分: 1.98分 (超过1.5分阈值)
```

## 使用方法

### 正常启动
```bash
python start_tpdog_monitor.py
```

程序会在每2分钟获取版块数据后自动进行断层分析。

### 测试功能
```bash
python test_gap_detection.py
```

运行测试脚本验证断层检测功能是否正常工作。

## 依赖要求

- 需要`dynamic_gap_detector.py`文件在同一目录下
- 需要pandas、numpy等数据处理库
- 需要配置TPDOG_TOKEN环境变量

## 注意事项

1. **最小化修改**: 本次修改遵循"最小化修改"原则，只添加断层检测功能，不修改任何无关代码
2. **兼容性**: 如果`dynamic_gap_detector.py`不存在，程序仍可正常运行，只是不会显示断层分析
3. **数据要求**: 断层分析需要至少4个正流入的板块数据才能进行
4. **实时性**: 断层分析会在每次获取版块数据后立即执行

## 技术细节

### 数据格式转换
程序会自动将TPDOG的数据格式转换为`dynamic_gap_detector`期望的格式：
- `name` → `名称`
- `m_net` → `今日主力净流入-净额`

### 错误处理
- 模块导入失败时会显示警告但不影响主程序运行
- 数据不足时会显示相应提示信息
- 分析过程中的异常会被捕获并显示错误信息

## 更新日志

- **2025-07-29**: 初始版本，添加基础断层检测功能
  - 集成`dynamic_gap_detector`的核心算法
  - 支持概念和行业板块断层分析
  - 添加测试脚本验证功能
